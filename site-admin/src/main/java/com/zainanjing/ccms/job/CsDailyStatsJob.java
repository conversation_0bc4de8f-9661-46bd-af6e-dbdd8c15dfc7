package com.zainanjing.ccms.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.JsonNode;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtil;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.http.HttpUtil;
import com.ruoyi.common.utils.id.IdUtil;
import com.zainanjing.ccms.track.domain.CsAppTrack;
import com.zainanjing.ccms.track.domain.CsAppTrackH5;
import com.zainanjing.ccms.track.domain.CsDailyStats;
import com.zainanjing.ccms.track.service.ICsAppTrackH5Service;
import com.zainanjing.ccms.track.service.ICsAppTrackService;
import com.zainanjing.ccms.track.service.ICsDailyStatsService;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class CsDailyStatsJob {
    private static final Logger log = LoggerFactory.getLogger(CsDailyStatsJob.class);


    private Long STAND_MINUTES = 1000 * 60 * 5L;

    @Resource
    private ICsDailyStatsService csDailyStatsService;

    @Resource
    private ICsAppTrackH5Service csAppTrackH5Service;

    @Resource
    private ICsAppTrackService csAppTrackService;

    /**
     * 计算日活数据
     */
    public void calculateDailyStats() {
        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(1);

        CsDailyStats csDailyStats = csDailyStatsService.lambdaQuery()
                .eq(CsDailyStats::getStatDate, yesterday)
                .oneOpt()
                .orElse(null);
        if (csDailyStats == null) {
            csDailyStats = new CsDailyStats();
            csDailyStats.setStatDate(yesterday);
        }else{
            csDailyStats.setUpdateTime(null);
        }
        //计算H5日活
        Long startTimeLong = yesterday.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long endTimeLong = today.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long h5Dau = csAppTrackH5Service.query().ge("record_ts", startTimeLong)
                .lt("record_ts", endTimeLong)
                .select("distinct cookie_id").count();
        csDailyStats.setH5Dau(h5Dau);
        csDailyStats.setAppDau(0L);
        csDailyStats.setAppTotalDuration(0L);
        //计算APP日活
        Set<String> deviceIds = new HashSet<>();
        long current = 1;
        long page = 1000;
        while (true) {
            Page<CsAppTrack> csAppTrackPage = csAppTrackService.lambdaQuery()
                    .ge(CsAppTrack::getRecordTs, startTimeLong)
                    .lt(CsAppTrack::getRecordTs, endTimeLong)
                    .orderByAsc(CsAppTrack::getRecordTs)
                    .page(Page.of(current, page, false));
            for (CsAppTrack csAppTrack : csAppTrackPage.getRecords()) {
                deviceIds.add(csAppTrack.getDeviceId());
                if (Set.of(1, 2).contains(csAppTrack.getDurationType())) {
                    csDailyStats.setAppTotalDuration(csDailyStats.getAppTotalDuration() + csAppTrack.getDuration());
                }
            }
            if (csAppTrackPage.getRecords().size() < page) {
                break;
            }
            current++;
        }
        csDailyStats.setAppDau(Long.valueOf(deviceIds.size()));

        if (csDailyStats.getAppDau() > 0) {
            csDailyStats.setAppAvgDuration(csDailyStats.getAppTotalDuration() / csDailyStats.getAppDau());
        }
        csDailyStats.setTotalDau(csDailyStats.getAppDau() + csDailyStats.getH5Dau());

        csDailyStatsService.saveOrUpdate(csDailyStats);
    }


    private final String APP_KEY = "kanjianchangchunApp";
    private final String APP_SECRET = "05365c57d47a4ec09eae076aa417dd34";

//    1.统计周期。上月21日0时至当月20日24时。
//            2.上报接口。
//    获取授权码接口：https://newsdata.xuexi.cn/gateway/open/api/oauth2/authorize
//    获取accessToken接口：https://newsdata.xuexi.cn/gateway/open/api/oauth2/accessToken
//    上报接口：https://newsdata.xuexi.cn/gateway/open/api/oauth2/data/reporting
//            3.上报时间。
//    根据要求，为规范客户端数据自动上报标准和程序，请各参评客户端将数据自动上报时间统一设置为每月23日3时00分00秒，评价系统接收端已完成相应修改，对上传时间不是规定整点时间（考虑了延时误差）的客户端数据将不予采信。
//            4.对接测试：8月4日全天、8月21日全天。
//            5.正式上报：8月23日3时。

    private final String AUTHORIZE_URL = "https://newsdata.xuexi.cn/gateway/open/api/oauth2/authorize";
    private final String ACCESS_TOKEN_URL = "https://newsdata.xuexi.cn/gateway/open/api/oauth2/accessToken";
    private final String REPORTING_URL = "https://newsdata.xuexi.cn/gateway/open/api/oauth2/data/reporting";

    private record ResData(String start_time, String end_time, Long app_adu, Long h5_dau, Long app_avg_duration) {
    }

    private record ResDTO(String appKey, String type, List<ResData> list, String accessToken) {
    }


    /**
     * 发送月活数据
     */
    public void calculateMonthlyStats() {
        //获取上个月21号到这个月20号的数据
        LocalDate startDate = LocalDate.now().minusMonths(1).withDayOfMonth(21);
        LocalDate endDate = LocalDate.now().withDayOfMonth(20);
        QueryWrapper<CsDailyStats> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("avg(h5_dau) as h5_dau", "avg(app_dau) as app_dau", "avg(app_avg_duration) as appAvgDuration");
        queryWrapper.between("stat_date", startDate, endDate);
        CsDailyStats csDailyStats = csDailyStatsService.getOne(queryWrapper);
        if (csDailyStats == null) {
            log.error("没有找到月活数据: {}", queryWrapper);
            return;
        }

        ResData resData = new ResData(DateUtil.format(startDate.atStartOfDay(), "yyyy-MM-dd HH:mm:ss"),
                DateUtil.format(endDate.plusDays(1).atStartOfDay(), "yyyy-MM-dd HH:mm:ss"),
                csDailyStats.getAppDau(),
                csDailyStats.getH5Dau(),
                csDailyStats.getAppAvgDuration());

        String token = getAuthorizeCode();

        ResDTO resDTO = new ResDTO(APP_KEY, "1", List.of(resData), token);
        log.debug("上报数据: {}", resDTO);
        JsonNode post = HttpUtil.post(REPORTING_URL, resDTO, null);
        log.debug("上报结果: {}", post);
    }

    private String getAuthorizeCode() {
        JsonNode post = HttpUtil.post(AUTHORIZE_URL, Map.of("appKey", APP_KEY,
                "responseType", "code",
                "state", IdUtil.fastSimpleUUID()
        ));

        if (StrUtil.equals("0000", post.get("code").asText())) {
            String authorizeCode = post.get("data").get("authorizeCode").asText();
            JsonNode tokenRes = HttpUtil.post(ACCESS_TOKEN_URL, Map.of("appKey", APP_KEY,
                    "appSecret", APP_SECRET,
                    "grantType", "authorization_code",
                    "authorizeCode", authorizeCode,
                    "state", IdUtil.fastSimpleUUID()
            ));
            if (StrUtil.equals("0000", tokenRes.get("code").asText())) {
                return tokenRes.get("data").get("accessToken").asText();
            }
        }
        throw new ServiceException("获取授权码失败");
    }

}
