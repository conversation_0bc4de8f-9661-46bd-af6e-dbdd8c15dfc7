package com.zainanjing.ccms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * Created by AllanLoo on 2018/9/5.
 */
@SpringBootApplication(scanBasePackages = {"com.zainanjing", "com.ruoyi"})
@MapperScan(value = {"com.zainanjing.**.mapper", "com.ruoyi.**.mapper"})
@ServletComponentScan({"com.zainanjing"})
@EnableAsync
public class AdminBootstrap {

    public static void main(String[] args) {
        SpringApplication.run(AdminBootstrap.class, args);
    }

}
