#测试环境配置
spring:
  main:
    lazy-initialization: true
  datasource:
    druid:
      # 主库数据源
      master:
        url: **********************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
      gdmm:
        enabled: true
        url: ************************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
  data:
    redis:
      # 地址
      host: *************
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 1
      # 密码
      password: K4UMDap0
  #配置虚拟目录
  elasticsearch:
    rest:
      uris: *************:9200
      password: 6jDhjr^GQGqv
      username: elastic
  http:
    multipart:
      maxFileSize: 200Mb #上传文件大小
      maxRequestSize: 200Mb
  servlet:
    multipart:
      location: /Users/<USER>/Desktop/temp
      max-file-size: 200MB
      max-request-size: 200MB

server:
  servlet:
    context-path: /${system.city}/admin-api
  port: 9085

system:
  api-url: https://ccms.gdmmdev.com/${system.city}/api
  push:
    is-production: false
  goods:
    url: https://wapinter${system.city}t.zainanjing365.com/siteapp/gdmm/invoke
  jwt-login:
    url: https://imagecensoringt.zainanjing365.com/tool/jwt/jiemi
    name: convenience-admin
    appid: n30fvfp2hx4nqnxj2mgplzujckompq0b
    key: 5OSr108V3Fozaoz6GZPP7vvLpcnNmjpT
  schedule:
    url: https://oriental${system.city}t.zainanjing365.com
    key: 3jzkn6ae2lpgfx52bqvj6r2cwsrsnyae
  im-url: https://imt.njgdmm.com/${system.city}



push:
  production: false
  cMasterSecret: a8d3548822a1f146bbf6a0b2 #城市配置
  cAppKey: 3fc7bd38231bbae63df250c1 #城市配置

ali-oss:
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  access-key-id: LTAI5tBGfaG9WiDNUu2dkB52
  access-key-secret: ******************************
  bucket-name: zainanjingshoptest
  url: https://img.shoptest.zainanjing365.com/
  role-arn : acs:ram::1177115587074564:role/ramosstest
  privateKey: MIICcwIBADANBgkqhkiG9w0BAQEFAASCAl0wggJZAgEAAoGBAJRUM4/uVVjp03MF2K/acMGk40zQK0BPeZVlgoLRW7oU1+/IFnx2loDlIWSH0UBpdCKFcHboh6bBMOJMjfE9TmpeCDcvVy1YwkAeXCNcuYT4FC5pphVe6c6K3ygsQquobgLhPzAE7BP6ezFL0A+rLB+D5HDDD1zfUa+jfJ0TPoxFAgMBAAECf2oFTffbiZSIWoYDI4Vbf70MCVsEkpBYYGB1fzX5u+fSsWP7LY7ivUH1dAa4c/+A+ltdH2gjGcHGf52nvKs2ZtD42Q8G9EHUQX8Cheg9jdb95mXOVQAuWAhyQkalRSDxeF7kj/C9DvMuaAho7j7L15AvrLL8ttvlihVnHABJ2nECQQDJPnNUt63I2iTN1Ji69xYQide7nyr8ztXScdg8CUIlEUAE/XN3bZQd8O48hfQu2gCbvytmX8zB0Aqwj3A+aEBxAkEAvK/60wborSbRbG/H3grKeiB4P44ThRa7RRaEgzLNgJDbEZtwKhI1ARe7MNL46kd/hGlUwh7KpkmkAP8fuubzFQJALUiTNR37yfVkULilAXU2DtSlqb9gMfDs5mTdrmy66JJgIH9Tqy0KJk6fnRjbYGuHtYQn5OprrbT9oss0C6bz0QJAeqq1l+eP8LGhyKtK9pFxXMArLLZC/mzuxlWDRIAMlZ6WrPWuj8tSZil1aqJdbIGa5wxLLAA0orKRSoyRpkJqmQJAF2DwAk6JkhJOtVvcWGLaCPWdot9VE6JEK1au7CuelsuUGp6z/2PQH/TeChgW8RjehvEBHLRAvhQwa/+7t0sfHw==
  publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUVDOP7lVY6dNzBdiv2nDBpONM0CtAT3mVZYKC0Vu6FNfvyBZ8dpaA5SFkh9FAaXQihXB26IemwTDiTI3xPU5qXgg3L1ctWMJAHlwjXLmE+BQuaaYVXunOit8oLEKrqG4C4T8wBOwT+nsxS9APqywfg+Rwww9c31Gvo3ydEz6MRQIDAQAB

ruoyi:
  allowedOrigins:
    - https://*.zainanjing365.com
    - https://*.njgdmm.com
    - https://*.gdmmdev.com
    - http://*.znjdev.com:*