#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/official/*Mapper.xml,classpath*:/mapper/admin/*Mapper.xml,classpath*:/mapper/system/*Mapper.xml,classpath*:/mapper/article/*Mapper.xml,classpath*:/mapper/shequ/*Mapper.xml,classpath*:/mapper/bc/*Mapper.xml,classpath*:/mapper/quartz/*Mapper.xml
  typeAliasesPackage: com.zainanjing.**.domain,com.ruoyi.**.domain
  global-config:
    db-config:
      logic-delete-field: deleteFlag # 全局逻辑删除的实体字段名(since 3.3.0,配置后可以忽略不配置步骤2)
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
      id-type: auto
    #字段策略 0:"忽略判断",1:"非 NULL 判断"),2:"非空判断"
    field-strategy: 2
    #驼峰下划线转换
    db-column-underline: true
    #刷新mapper 调试神器
#    refresh-mapper: true
    #数据库大写下划线转换
    #capital-mode: true
    #序列接口实现类配置
    #key-generator: com.baomidou.springboot.xxx
    #逻辑删除配置
    #logic-delete-value: 0
    #logic-not-delete-value: 1
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
    #sql-injector: com.baomidou.springboot.xxx
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false

spring:
  threads:
    virtual:
      enabled: true
  # 默认的profile为dev，其他环境通过指定启动参数使用不同的profile，比如：
  #   测试环境：java -jar my-spring-boot.jar --spring.profiles.active=test
  #   生产环境：java -jar my-spring-boot.jar --spring.profiles.active=prod
  profiles:
    active: test
  #关闭banner
  main:
    banner-mode: "off"
  #thymeleaf config
  thymeleaf:
    mode: HTML
    encoding: UTF-8
    content-type: text/html
    cache: false
  #i18n config
  messages:
    basename: i18n/messages
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      # 从库数据源
      slave:
        # 从数据源开关/默认关闭
        enabled: false
        url:
        username:
        password:
      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: false
      statViewServlet:
        enabled: false
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: druidBEsjvnE3
        login-password: 52fQe7UuyKMXjSfL
      filter:
        stat:
          enabled: false
          # 慢SQL记录
          log-slow-sql: false
          log-executable-sql: false
          slow-sql-millis: 3000
#          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  #redis
  data:
    redis:
      timeout: 30s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 100
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms
  jms:
    pub-sub-domain: false
  activemq:
    pool:
      enabled: true
      max-connections: 100
      idle-timeout: 30000
  web:
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/, classpath:/static/, classpath:/public/, file:${file-upload.root-dir}
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration,org.springframework.boot.autoconfigure.elasticsearch.ElasticsearchRestClientAutoConfiguration,org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration


# 项目相关配置
ruoyi:
  # 名称
  name: 融媒内容管理系统
  # 版本
  version: 3.8.4
  # 版权年份
  copyrightYear: 2022
  demoEnabled: false
  profile: ${system.city}/ccms #城市配置
  # 获取ip地址开关
  addressEnabled: true
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: zaaclollrpsmycwljsozicbvuaeevweduzezriaiavfgbtqbfxqfm
  # 令牌有效期（默认30分钟）
  expireTime: 30000

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

sms:
  template:
    commonCaptchaTemplate: SMS_178456933 #通用验证码模板
    forgetPwdCaptchaTemplate: SMS_178456933 #忘记密码验证码短信模板
    registerCaptchaTemplate: SMS_178461944 #注册验证码短信模板
    receiptCaptchaTemplate: SMS_261135317 #回执验证码短信模板
    notifyMerchantTemplate: SMS_253535016 #5分钟通知商户接单短信模板

logging:
  level:
    ROOT: INFO
    com.zainanjing: DEBUG
    com.ruoyi: DEBUG
    com.baomidou.example.mapper: DEBUG
    druid.sql.Statement: DEBUG

cache:
  framework: redis
  ttl: #设置缓存时间秒
    passwordRetryCache: 600

system:
  city: changchun
  tenants:
    flag: false
  push:
    is-production: true
  login:
    publicKey: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZlm37UcIkgNai4sk2HOnUOb6mJK+Ije+TYA2eaA+fn0WVCkYDA5Ld7UioVEoTbPLWa3plODmw/cczig465Gy0LTuJIfJ6ioKp2kQX8VIzFPWfU1KiEGmnkbe2Y/7dWHG0N6abbykghjLe39h91mRkmKAj4sa0SNcKX92GUzWFVQIDAQAB
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJmWbftRwiSA1qLiyTYc6dQ5vqYkr4iN75NgDZ5oD5+fRZUKRgMDkt3tSKhUShNs8tZremU4ObD9xzOKDjrkbLQtO4kh8nqKgqnaRBfxUjMU9Z9TUqIQaaeRt7Zj/t1YcbQ3pptvKSCGMt7f2H3WZGSYoCPixrRI1wpf3YZTNYVVAgMBAAECgYEAho9/KDMHTwf6BJeBBgHq817sI4/8oj0Iayw6glBVb9oREnT/xeA5qmobwC6o/7/yOd3MTCYolWKiRgtsUDiU6x6K943NaBovyycZotlkUWl5jxMg/bh/Wai0Wbujo16g71xGIMkSBZ3FtzvdEa6XUavsS997/dji87LK1LLZK/kCQQDp4nn2F2l+CNT/1KqSU23qSsoYaO21x2weZiWS06+JS2dyLj/o52W6L3UirzxsQHjoMS7YdEL4gMJn0wclW0T3AkEAqBw+lHUgjGRDlx3oXAwf1hfy1zO2XNci7+z2H7aFewZQfkmZwpzeMI5j0eIQT1J+SgMdeKUCOuNPkUBrqT0REwJAEJK/2bRnAUtW7JoBTEUlMMkD8R7eWVc8Thsm5nShp7T0K58jYDmld8ANKNwKCBG5f5qcmzIB3OIi5ICLrRX+mQJAaZksWjbk1itZJcC8p61aBjMO8M1j/ReKs6lmxW7yk9/YiiuTcWL2UwvuefWGBz2rZLRmO9Vy/CizX2tW9X0NjwJAc94FVKIq+GO59SJ54LC5LCon9WMRXSogUbJEVbvZdg2ODNZV9rtX8UpYIVnvBtroQ/MPP/EJBTpJaYbI1mfViw==
  water: image/watermark,image_aGVmZWkvb2ZmaWNpYWwvdGVtcGxhdGUvd2F0ZXJfaW1nLnBuZw==

#消息推送
push:
  production: false

city-enums:
  LOCATION: ${system.city}
  LOCATION_ZAI: 在${city-enums.CITY_ADDRESS_NAME}
  LOCATION_APP: ${system.city}
  CITY_ADDRESS_NAME: 长春
  CITY_NAME_FOR_BUSINESS: ${city-enums.CITY_ADDRESS_NAME}2.0
  BUSINESS_URL: https://nnisei${system.city}new.zainanjing365.com/businessApp
  APP_SCHEMA: gdmm-zai${system.city}