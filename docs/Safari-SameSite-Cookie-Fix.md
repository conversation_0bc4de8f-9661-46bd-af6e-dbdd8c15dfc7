# Safari浏览器SameSite=None Cookie兼容性解决方案

## 问题描述

在跨域场景下，为了让Cookie能够正常发送，通常需要设置`SameSite=None`属性。但是某些版本的Safari浏览器和Chrome浏览器不支持`SameSite=None`属性，甚至会将其错误地解析为`SameSite=Strict`，导致跨域Cookie无法正常工作。

## 受影响的浏览器版本

### 不支持SameSite=None的浏览器版本：

1. **iOS Safari**：iOS 12及以下版本
2. **macOS Safari**：macOS 10.14及以下版本
3. **Chrome**：51-66版本
4. **Chromium**：51-66版本
5. **UC浏览器**：12.13以下版本

### 问题表现：
- 这些浏览器会将`SameSite=None`错误地解析为`SameSite=Strict`
- 导致跨域场景下Cookie无法发送
- 用户登录状态丢失或追踪功能失效

## 解决方案

### 1. 创建CookieUtil工具类

我们创建了一个`CookieUtil`工具类，通过检测浏览器User-Agent来判断是否应该设置`SameSite=None`属性：

```java
// 检测浏览器是否支持SameSite=None
boolean shouldUseSameSiteNone = CookieUtil.shouldUseSameSiteNone(userAgent);

// 创建跨域兼容的Cookie
Cookie cookie = CookieUtil.createCrossDomainCookie(
    "cookieName", 
    "cookieValue", 
    "/path", 
    maxAge, 
    secure, 
    httpOnly, 
    request
);
```

### 2. 检测逻辑

工具类通过正则表达式检测以下浏览器版本：

- **Chrome/Chromium 51-66**：不设置SameSite=None
- **iOS 12及以下**：不设置SameSite=None
- **macOS 10.14及以下**：不设置SameSite=None
- **UC浏览器 12.13以下**：不设置SameSite=None
- **其他浏览器**：正常设置SameSite=None

### 3. 在TrackController中的应用

原来的代码：
```java
trackCookie.setAttribute("SameSite", "None");  // 跨域场景必须设置
```

修改后的代码：
```java
// 使用工具类创建跨域兼容的Cookie
Cookie trackCookie = CookieUtil.createCrossDomainCookie(
    H5_TRACK_COOKIE_NAME, 
    userId, 
    "/changchun", 
    365 * 24 * 60 * 60,  // 有效期1年
    true,  // 仅在HTTPS下传输
    true,  // 防止JS读取
    request
);
```

## 技术实现细节

### User-Agent检测正则表达式

```java
private static final Pattern CHROME_PATTERN = Pattern.compile("Chrome/([0-9]+)");
private static final Pattern IOS_PATTERN = Pattern.compile("OS ([0-9]+)_([0-9]+)");
private static final Pattern MACOS_PATTERN = Pattern.compile("OS X ([0-9]+)_([0-9]+)");
private static final Pattern UC_BROWSER_PATTERN = Pattern.compile("UCBrowser/([0-9]+)\\.([0-9]+)");
private static final Pattern CHROMIUM_PATTERN = Pattern.compile("Chromium/([0-9]+)");
```

### 兼容性策略

1. **支持的浏览器**：正常设置`SameSite=None; Secure`
2. **不支持的浏览器**：不设置SameSite属性，让浏览器使用默认行为
3. **未知浏览器**：默认设置`SameSite=None`（保守策略）

## 测试验证

我们提供了完整的单元测试来验证不同浏览器User-Agent的检测逻辑：

```java
@Test
public void testShouldUseSameSiteNone_Safari_iOS() {
    // iOS 13+ 支持
    assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 13_0 like Mac OS X)..."));
    
    // iOS 12及以下不支持
    assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X)..."));
}
```

## 使用建议

1. **HTTPS环境**：`SameSite=None`必须配合`Secure`属性使用
2. **日志记录**：工具类会记录检测结果，便于调试
3. **定期更新**：随着浏览器版本更新，可能需要调整检测逻辑
4. **测试覆盖**：在不同浏览器环境下测试Cookie功能

## 相关文件

- `site-common/src/main/java/com/ruoyi/common/utils/cookie/CookieUtil.java` - 工具类实现
- `site-api/src/main/java/com/zainanjing/ccms/controller/TrackController.java` - 使用示例
- `site-api/src/test/java/com/ruoyi/common/utils/cookie/CookieUtilTest.java` - 单元测试

## 参考资料

- [SameSite Cookie Attribute Changes](https://auth0.com/docs/manage-users/cookies/samesite-cookie-attribute-changes)
- [Chrome SameSite Cookie Changes](https://developers.google.com/search/blog/2020/01/get-ready-for-new-samesitenone-secure)
- [Safari SameSite Cookie Support](https://webkit.org/blog/8613/intelligent-tracking-prevention-2-1/)
