package com.ruoyi.common.utils.cookie;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * CookieUtil测试类
 * 测试不同浏览器User-Agent的SameSite=None兼容性检测
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class CookieUtilTest {

    @Test
    public void testShouldUseSameSiteNone_Chrome() {
        // 测试支持的Chrome版本
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/80.0.3987.149 Safari/537.36"));
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"));
        
        // 测试不支持的Chrome版本 (51-66)
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36"));
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36"));
    }

    @Test
    public void testShouldUseSameSiteNone_Safari_iOS() {
        // 测试支持的iOS版本 (iOS 13+)
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 13_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0 Mobile/15E148 Safari/604.1"));
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"));
        
        // 测试不支持的iOS版本 (iOS 12及以下)
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1"));
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPhone; CPU iPhone OS 11_0 like Mac OS X) AppleWebKit/604.1.38 (KHTML, like Gecko) Version/11.0 Mobile/15A372 Safari/604.1"));
        
        // 测试iPad
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPad; CPU OS 13_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.0 Mobile/15E148 Safari/604.1"));
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (iPad; CPU OS 12_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.0 Mobile/15E148 Safari/604.1"));
    }

    @Test
    public void testShouldUseSameSiteNone_Safari_macOS() {
        // 测试支持的macOS版本 (macOS 10.15+)
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15"));
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Macintosh; Intel Mac OS X 11_0_0) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.1 Safari/605.1.15"));
        
        // 测试不支持的macOS版本 (macOS 10.14及以下)
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_14_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/13.1.2 Safari/605.1.15"));
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/12.1.2 Safari/605.1.15"));
    }

    @Test
    public void testShouldUseSameSiteNone_UCBrowser() {
        // 测试支持的UC浏览器版本
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Linux; U; Android 9; zh-CN; MI 9 Build/PKQ1.181121.001) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/69.0.3497.100 UCBrowser/12.13.2.1208 Mobile Safari/537.36"));
        
        // 测试不支持的UC浏览器版本
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Linux; U; Android 8.1.0; zh-CN; EML-AL00 Build/HUAWEIEML-AL00) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/57.0.2987.108 UCBrowser/12.12.9.1158 Mobile Safari/537.36"));
    }

    @Test
    public void testShouldUseSameSiteNone_Chromium() {
        // 测试支持的Chromium版本
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/80.0.3987.162 Safari/537.36"));
        
        // 测试不支持的Chromium版本 (51-66)
        assertFalse(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chromium/60.0.3112.113 Safari/537.36"));
    }

    @Test
    public void testShouldUseSameSiteNone_EdgeCases() {
        // 测试空字符串
        assertFalse(CookieUtil.shouldUseSameSiteNone(""));
        assertFalse(CookieUtil.shouldUseSameSiteNone(null));
        
        // 测试其他浏览器（应该返回true，因为没有特殊处理）
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/18.18363"));
        assertTrue(CookieUtil.shouldUseSameSiteNone("Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0"));
    }
}
