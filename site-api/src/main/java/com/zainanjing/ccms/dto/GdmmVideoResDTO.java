package com.zainanjing.ccms.dto;

import com.zainanjing.article.domain.GdmmVideo;
import lombok.Data;

@Data
public class GdmmVideoResDTO extends GdmmVideo {

    private Integer isExamine;//评论是否需要审核
    //当前用户是否点赞
    private Integer isPraise;
    //当前用户是否收藏
    private Integer isCollect;
    //当前用户收藏id
    private Integer collectId;
    //短视频收藏数
    private Integer collectNum;
    private String source; //来源
    private String sourceImgUrl; //来源图片
    //分类字典module
    private String module;
    //分享图片
    private java.lang.String shareImgUrl;

    public GdmmVideoResDTO(GdmmVideo gdmmVideo) {
        this.setId(gdmmVideo.getId());
        this.setCatId(gdmmVideo.getCatId());
        this.setSubjectId(gdmmVideo.getSubjectId());
        this.setSubjectCatId(gdmmVideo.getSubjectCatId());
        this.setType(gdmmVideo.getType());
        this.setTitle(gdmmVideo.getTitle());
        this.setViewed(gdmmVideo.getViewed());
        this.setInitView(gdmmVideo.getInitView());
        this.setCommentNum(gdmmVideo.getCommentNum());
        this.setPraiseNum(gdmmVideo.getPraiseNum());
        this.setInitPraiseNum(gdmmVideo.getInitPraiseNum());
        this.setSourceId(gdmmVideo.getSourceId());
        this.setAuthor(gdmmVideo.getAuthor());
        this.setEditor(gdmmVideo.getEditor());
        this.setContent(gdmmVideo.getContent());
        this.setVideoUrl(gdmmVideo.getVideoUrl());
        this.setVideoSecond(gdmmVideo.getVideoSecond());
        this.setVideoImgUrl(gdmmVideo.getVideoImgUrl());
        this.setVideoImgWidth(gdmmVideo.getVideoImgWidth());
        this.setVideoImgHeight(gdmmVideo.getVideoImgHeight());
        this.setIsRecommend(gdmmVideo.getIsRecommend());
        this.setIsComment(gdmmVideo.getIsComment());
        this.setSort(gdmmVideo.getSort());
        this.setStatus(gdmmVideo.getStatus());
        this.setEnabled(gdmmVideo.getEnabled());
        this.setManagerId(gdmmVideo.getManagerId());
        this.setCreateTime(gdmmVideo.getCreateTime());
        this.setUpdateTime(gdmmVideo.getUpdateTime());
        this.setLabelId(gdmmVideo.getLabelId());
        this.setIdForThird(gdmmVideo.getIdForThird());
        this.setCardId(gdmmVideo.getCardId());
        this.setPublishStatus(gdmmVideo.getPublishStatus());
        this.setPublishTime(gdmmVideo.getPublishTime());
        this.setSourceNameForThird(gdmmVideo.getSourceNameForThird());
    }
}
