package com.zainanjing.ccms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Created by AllanLoo on 2018/5/9.
 */
@SpringBootApplication(scanBasePackages = {"com.zainanjing", "com.ruoyi"}, exclude = DataSourceAutoConfiguration.class)
@MapperScan(value = {"com.zainanjing.**.mapper", "com.ruoyi.**.mapper"})
@EnableScheduling
@EnableAsync
public class ApiBootstrap {
    public static void main(String[] args) {
        SpringApplication.run(ApiBootstrap.class, args);
    }

}