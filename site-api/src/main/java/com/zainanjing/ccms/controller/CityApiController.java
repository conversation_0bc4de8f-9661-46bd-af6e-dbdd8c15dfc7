package com.zainanjing.ccms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.common.utils.CollUtil;
import com.ruoyi.common.utils.DBSwitcher;
import com.ruoyi.common.utils.OSSUtil;
import com.ruoyi.common.utils.StrUtil;
import com.zainanjing.article.domain.*;
import com.zainanjing.article.service.*;
import com.zainanjing.common.constant.Constants;
import com.zainanjing.common.constant.RedisKey;
import com.zainanjing.common.util.CommonUtil;
import com.zainanjing.common.util.RedisUtil;
import com.zainanjing.ccms.dto.GdmmArticleResDTO;
import com.zainanjing.ccms.dto.GdmmArticleSubjectResDTO;
import com.zainanjing.ccms.dto.GdmmVideoResDTO;
import com.zainanjing.convenience.support.web.ResResult;
import com.zainanjing.convenience.utils.WangyiCheckUtil;
import com.zainanjing.official.domain.Article;
import com.zainanjing.official.search.EArticleService;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api-mix")
public class CityApiController {

    private final String SEARCH_ALL = "0";
    private final String SEARCH_ARTICLE = "1";//资讯
    private final String SEARCH_BCFM = "2";//电台 听广播 TODO 不需要
    private final String SEARCH_BCFORUM = "3"; //节目 TODO 不需要
    private final String SEARCH_VIDEO = "4"; //短视频
    private final String SEARCH_DOCUMENTARY = "5"; //纪录片
    private final String SEARCH_PAPER = "6";//广电报 TODO 不需要
    private final String SEARCH_ARTICLESUBJECT = "7";//专题
    private final String SEARCH_BCPROGRAMTVLIVE = "8";//看电视 TODO 不需要
    private final String SEARCH_BCPROGRAMLIVE = "9";//直播 TODO 不需要
    private final String SEARCH_OFFICIAL = "10";//新增订阅号

    @Resource
    private IGdmmSpecialWordService gdmmSpecialWordService;
    @Resource
    private IGdmmVideoService gdmmVideoService;

    @Resource
    private IGdmmDictionaryService gdmmDictionaryService;

    @Resource
    private IGdmmArticleSubjectService gdmmArticleSubjectService;

    @Resource
    private IGdmmArticleService gdmmArticleService;

    @Resource
    private EArticleService eArticleService;

    @Resource
    private WangyiCheckUtil wangyiCheckUtil;

    @GetMapping("/searchAllNews")
    public ResResult searchAllNews(@RequestParam(value = "currentPage", defaultValue = "1") Integer page,
                                   @RequestParam(value = "pageSize", defaultValue = "3") Integer pageSize,
                                   @RequestParam(value = "keyword", required = false) String keyword,
                                   @RequestParam(value = "type", defaultValue = "0") String type) {
        Map<String, Object> resultMap = new HashMap<>();
        try (DBSwitcher switcher = new DBSwitcher(DataSourceType.SLAVE.toString())) {
            // 敏感词检查
            List<GdmmSpecialWord> list = gdmmSpecialWordService.lambdaQuery().eq(GdmmSpecialWord::getType, 0).list();
            if (com.ruoyi.common.utils.CollUtil.isNotEmpty(list)) {
                com.ruoyi.common.utils.dfa.WordTree tree = new com.ruoyi.common.utils.dfa.WordTree();
                list.stream().map(GdmmSpecialWord::getName).forEach(tree::addWord);
                List<String> matchAllTitle = tree.matchAll(keyword, -1, false, false);
                matchAllTitle = com.ruoyi.common.utils.CollUtil.distinct(matchAllTitle);
                if (com.ruoyi.common.utils.CollUtil.isNotEmpty(matchAllTitle)) {
                    return ResResult.error(com.ruoyi.common.utils.StrUtil.format("搜索关键词中含有敏感词{},提交失败", matchAllTitle));
                }
            }

            wangyiCheckUtil.checkText("GdmmSearchServiceImpl_searchAll",keyword);

            if (SEARCH_ALL.equals(type) || SEARCH_ARTICLE.equals(type)) {
                IPage<GdmmArticleResDTO> articleResDTOPage = gdmmArticleService.lambdaQuery()
                        .select(GdmmArticle::getId, GdmmArticle::getArticleCatId, GdmmArticle::getType,
                                GdmmArticle::getTitle, GdmmArticle::getFontSize, GdmmArticle::getArticleType,
                                GdmmArticle::getIsHot, GdmmArticle::getRemark, GdmmArticle::getImgUrl,
                                GdmmArticle::getViewed, GdmmArticle::getSort, GdmmArticle::getAuthor,
                                GdmmArticle::getIsHighLight, GdmmArticle::getCreateTime, GdmmArticle::getDisplayMode,
                                GdmmArticle::getSourceId, GdmmArticle::getSourceNameForThird, GdmmArticle::getLabelId,
                                GdmmArticle::getOpenType, GdmmArticle::getPublishTime, GdmmArticle::getInitView,GdmmArticle::getIsComment,GdmmArticle::getVideoSecond,
                                GdmmArticle::getViewTimes, GdmmArticle::getLinkUrl)
                        .eq(GdmmArticle::getStatus, 0)
                        .eq(GdmmArticle::getEnabled, 0)
                        .eq(GdmmArticle::getPublishStatus, 0)
                        .like(StrUtil.isNotBlank(keyword), GdmmArticle::getTitle, keyword)
                        .orderByDesc(GdmmArticle::getSort)
                        .orderByDesc(GdmmArticle::getId)
                        .page(Page.of(page, pageSize, false)).convert(v -> new GdmmArticleResDTO(v));
                if (CollUtil.isNotEmpty(articleResDTOPage.getRecords())) {
                    Set<Integer> sourceIds = articleResDTOPage.getRecords().stream().map(GdmmArticleResDTO::getSourceId).collect(Collectors.toSet());
                    sourceIds.remove(Integer.valueOf(0));
                    final Map<Integer, GdmmDictionary> sourceMaps = new HashMap<>();
                    if (CollUtil.isNotEmpty(sourceIds)) {
                        sourceMaps.putAll(gdmmDictionaryService.lambdaQuery()
                                .eq(GdmmDictionary::getModule, "article")
                                .in(GdmmDictionary::getId, sourceIds)
                                .select(GdmmDictionary::getId, GdmmDictionary::getName, GdmmDictionary::getImgUrl)
                                .list().stream().collect(Collectors.toMap(GdmmDictionary::getId, v -> v)));
                    }
                    Map<String, Integer> switchInfoMap = getSwitchInfo();
                    articleResDTOPage.getRecords().forEach(v -> {
                        v.setImgUrl(OSSUtil.getImageURL(v.getImgUrl()));
                        v.setShareImgUrl(OSSUtil.getImageURL(v.getShareImgUrl()));
                        v.setViewed(CommonUtil.redisCommonFind(RedisKey.ARTICLE_CLICKNUM + "_" + v.getId()).intValue() + v.getInitView());
                        GdmmDictionary gdmmDictionary = sourceMaps.get(v.getSourceId());
                        if (gdmmDictionary != null) {
                            v.setSourceName(gdmmDictionary.getName());
                            v.setSource(gdmmDictionary.getName());
                            v.setSourceImgUrl(gdmmDictionary.getImgUrl());
                        }
                        assembleData(v, switchInfoMap);
                    });
                }
                resultMap.put("articleList", articleResDTOPage.getRecords());
            }
//        if (SEARCH_ALL.equals(type) || SEARCH_BCFM.equals(type)) {
//            //电台搜索
//            Page<BcFm> pageFm = bcFmService.lambdaQuery()
//                    .eq(BcFm::getIsShow, 1).eq(BcFm::getStatus, 0)
//                    .like(StrUtil.isNotBlank(keyword), BcFm::getName, keyword)
//                    .page(Page.of(page, pageSize, false));
//            resultMap.put("bcFmList", pageFm.getRecords());
//        }
//        if (SEARCH_ALL.equals(type) || SEARCH_BCFORUM.equals(type)) {
//            //节目
//            List<BcForum> bcForumList = bcForumService.searchBcForum(filterMap, startRow, pageSize);
//            resultMap.put("bcForumList", bcForumList);
//        }
            if (SEARCH_ALL.equals(type) || SEARCH_VIDEO.equals(type)) {
                //短视频
                List<GdmmDictionary> videoCats = gdmmDictionaryService.lambdaQuery().eq(GdmmDictionary::getModule, "video")
                        .select(GdmmDictionary::getId, GdmmDictionary::getName).list();
                if (CollUtil.isEmpty(videoCats)) {
                    resultMap.put("gdmmVideoList", List.of());
                } else {
                    Page<GdmmVideo> gdmmVideoPage = gdmmVideoService.lambdaQuery().in(GdmmVideo::getCatId, videoCats.stream().map(GdmmDictionary::getId).collect(Collectors.toList()))
                            .eq(GdmmVideo::getStatus, 0).like(StrUtil.isNotBlank(keyword), GdmmVideo::getTitle, keyword)
                            .eq(GdmmVideo::getPublishStatus, 0)
                            .eq(GdmmVideo::getEnabled, 0)
                            .orderByDesc(GdmmVideo::getSort)
                            .orderByDesc(GdmmVideo::getId)
                            .page(Page.of(page, pageSize, false));
                    resultMap.put("gdmmVideoList", handleVideoData(gdmmVideoPage.getRecords()));
                }
            }
            if (SEARCH_ALL.equals(type) || SEARCH_DOCUMENTARY.equals(type)) {
                //纪录片
                List<GdmmDictionary> videoCats = gdmmDictionaryService.lambdaQuery().eq(GdmmDictionary::getModule, "documentary")
                        .select(GdmmDictionary::getId, GdmmDictionary::getName).list();
                if (CollUtil.isEmpty(videoCats)) {
                    resultMap.put("gdmmDocumentaryList", List.of());
                } else {
                    Page<GdmmVideo> gdmmVideoPage = gdmmVideoService.lambdaQuery().in(GdmmVideo::getCatId, videoCats.stream().map(GdmmDictionary::getId).collect(Collectors.toList()))
                            .eq(GdmmVideo::getStatus, 0).like(StrUtil.isNotBlank(keyword), GdmmVideo::getTitle, keyword)
                            .eq(GdmmVideo::getPublishStatus, 0)
                            .eq(GdmmVideo::getEnabled, 0)
                            .orderByDesc(GdmmVideo::getSort)
                            .orderByDesc(GdmmVideo::getId)
                            .page(Page.of(page, pageSize, false));
                    resultMap.put("gdmmDocumentaryList", handleVideoData(gdmmVideoPage.getRecords()));
                }
            }
//        if (SEARCH_ALL.equals(type) || SEARCH_PAPER.equals(type)) {
//            //广电报
//            List<GdmmPaper> gdmmPaperList = gdmmPaperService.gdmmList(filterMap, startRow, pageSize);
//            resultMap.put("gdmmPaperList", gdmmPaperList);
//        }
            if (SEARCH_ALL.equals(type) || SEARCH_ARTICLESUBJECT.equals(type)) {
                Page<GdmmArticleSubject> gdmmArticleSubjectPage = gdmmArticleSubjectService.lambdaQuery()
//                        .eq(GdmmArticleSubject::getType, 0)
                        .eq(GdmmArticleSubject::getEnabled, 0)
                        .eq(GdmmArticleSubject::getStatus, 0)
                        .like(StrUtil.isNotBlank(keyword), GdmmArticleSubject::getTitle, keyword)
                        .orderByDesc(GdmmArticleSubject::getSort)
                        .orderByDesc(GdmmArticleSubject::getId)
                        .page(Page.of(page, pageSize, false));
                List<GdmmArticleSubjectResDTO> gdmmArticleSubjectResDTOS = new ArrayList<>();
                if (StrUtil.equals("1", RedisUtil.findString( RedisKey.ARTICLE_IS_SOURCE.getKey()))) {
                    Map<Integer, GdmmDictionary> sourceMaps = gdmmDictionaryService.lambdaQuery().eq(GdmmDictionary::getModule, "article")
                            .eq(GdmmDictionary::getCode, "source")
                            .select(GdmmDictionary::getId, GdmmDictionary::getName, GdmmDictionary::getImgUrl)
                            .list().stream().collect(Collectors.toMap(GdmmDictionary::getId, v -> v));
                    gdmmArticleSubjectPage.getRecords().forEach(v -> {
                        v.setImgUrl(OSSUtil.getImageURL(v.getImgUrl()));
                        GdmmDictionary gdmmDictionary = sourceMaps.get(v.getSourceId());
                        GdmmArticleSubjectResDTO gdmmArticleSubjectResDTO = new GdmmArticleSubjectResDTO(v);
                        if (gdmmDictionary != null) {
                            gdmmArticleSubjectResDTO.setSourceName(gdmmDictionary.getName());
                            gdmmArticleSubjectResDTO.setSourceImgUrl(gdmmDictionary.getImgUrl());
                        }
                        gdmmArticleSubjectResDTO.setIsSource(1);
                        gdmmArticleSubjectResDTOS.add(gdmmArticleSubjectResDTO);
                    });
                }
                resultMap.put("gdmmArticleSubjectList", gdmmArticleSubjectResDTOS);
            }
            //电视
//        if (SEARCH_ALL.equals(type) || SEARCH_BCPROGRAMTVLIVE.equals(type)) {
//            //filterMap.put("typeId", typeId);
//            filterMap.put("isShow", Constants.YES);
//            filterMap.put("status", Constants.FOU);
//            filterMap.put("now", CommonUtil.getTimestamp());
//
//            List<BcProgramTvLive> bcProgramTvLiveList = bcProgramTvLiveDao.list(filterMap, startRow, pageSize);
//            for (BcProgramTvLive bcProgramTvLive : bcProgramTvLiveList) {
//                bcProgramTvLive.setLogo(OSSUtil.getImageURL(bcProgramTvLive.getLogo()));
//                bcProgramTvLive.setShareImg(OSSUtil.getImageURL(bcProgramTvLive.getShareImg()));
//            }
//            resultMap.put("bcProgramTvLiveList", bcProgramTvLiveList);
//        }
            //直播
//        if (SEARCH_ALL.equals(type) || SEARCH_BCPROGRAMLIVE.equals(type)) {
//            List<BcProgramLive> bcProgramLiveList = new ArrayList<BcProgramLive>();
//            logger.error("搜索直播列表开始：");
//
//            //20170605 加
//            filterMap.put("isShow", Constants.SHI);
//
//            Integer now = CommonUtil.getTimestamp();
//            filterMap.put("now", now);
//
//            bcProgramLiveList = bcProgramLiveService.search(filterMap, startRow, pageSize);
//            if (bcProgramLiveList.size() > 0) {
//                for (BcProgramLive bp : bcProgramLiveList) {
//                    bp.setLogo(OSSUtil.getImageURL(bp.getLogo()));
//                    bp.setShareImg(OSSUtil.getImageURL(bp.getShareImg()));
//                    bp.setBanner(OSSUtil.getImageURL(bp.getBanner()));
//                    bp.setView(bp.getShowView());
//
//                    //修改最大点击量
//                    Long showView = bp.getShowView();
//                    Long maxView = bp.getMaxView();
//                    if (showView > maxView) {
//                        bp.setShowView(maxView);
//                    }
//                }
//            }
//            resultMap.put("bcProgramLiveList", bcProgramLiveList);
//        }
        }
        if (SEARCH_ALL.equals(type) || SEARCH_OFFICIAL.equals(type)) {
            IPage<Article> officialNewsList = eArticleService.list(page, pageSize, keyword, null);
            resultMap.put("officialNewsList", officialNewsList.getRecords());
        }
        return ResResult.success(resultMap);
    }

    private Integer getSettingValueByRedisKey(String key) {
        String valueStr = RedisUtil.findString( key);
        Integer value = StrUtil.isBlank(valueStr) ? 0 : Integer.valueOf(valueStr);//默认是关闭
        return value;
    }

    /**
     * 处理视频数据
     */
    private List<GdmmVideoResDTO> handleVideoData(List<GdmmVideo> gdmmVideoList) {
        List<GdmmVideoResDTO> gdmmVideoResDTOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(gdmmVideoList)) {
            Map<String, Integer> switchInfoMap = new HashMap<String, Integer>();
            //新闻是否开启评论(先根据总设置，然后再根据单条新闻设置)
            Integer isComment = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_OPEN_SHORT_VIDEO_COMMENT.getKey());
            switchInfoMap.put("isComment", isComment);
            //新闻是否开启评论审核
            Integer isExamine = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_AUDIT_SHORT_VIDEO_COMMENT.getKey());
            switchInfoMap.put("isExamine", isExamine);

            Set<Integer> sourceIds = gdmmVideoList.stream().map(GdmmVideo::getSourceId).collect(Collectors.toSet());
            sourceIds.remove(Integer.valueOf(0));
            final Map<Integer, GdmmDictionary> sourceMaps = new HashMap<>();
            if (CollUtil.isNotEmpty(sourceIds)) {
                sourceMaps.putAll(gdmmDictionaryService.lambdaQuery()
                        .eq(GdmmDictionary::getModule, "article")
                        .in(GdmmDictionary::getId, sourceIds)
                        .select(GdmmDictionary::getId, GdmmDictionary::getName, GdmmDictionary::getImgUrl)
                        .list().stream().collect(Collectors.toMap(GdmmDictionary::getId, v -> v)));
            }

            for (GdmmVideo gdmmVideo : gdmmVideoList) {
                GdmmVideoResDTO gdmmVideoResDTO = new GdmmVideoResDTO(gdmmVideo);
                //新闻是否开启评论(先根据总设置，然后再根据单条新闻设置)
                gdmmVideoResDTO.setIsComment(switchInfoMap.get("isComment") == 1 ? gdmmVideo.getIsComment() : 0);
                //新闻是否开启评论审核
                gdmmVideoResDTO.setIsExamine(switchInfoMap.get("isExamine"));

                gdmmVideoResDTO.setVideoImgUrl(OSSUtil.getImageURL(gdmmVideo.getVideoImgUrl()));

                gdmmVideoResDTO.setShareImgUrl(gdmmVideoResDTO.getVideoImgUrl());

                GdmmDictionary gdmmDictionary = sourceMaps.get(gdmmVideo.getSourceId());
                if (gdmmDictionary != null) {
                    gdmmVideoResDTO.setSource(gdmmDictionary.getName());
                    gdmmVideoResDTO.setSourceImgUrl(OSSUtil.getImageURL(gdmmDictionary.getImgUrl()));
                }
                Long id = gdmmVideo.getId();
//                String uid_videoId_key = RedisKey.VIDEO_PRAISE.getKeyForVideo(sessionUid, id.toString()); 暂时不要
                String videoId_key = RedisKey.VIDEO_PRAISE_NUM.getKey(id.toString());
//                Long isPraise = CommonUtil.redisCommonFind(uid_videoId_key, jedisTemplate);
                Long praiseNum = CommonUtil.redisCommonFind(videoId_key);
//                gdmmVideo.setIsPraise(isPraise.intValue());
                gdmmVideoResDTO.setPraiseNum(praiseNum.intValue() + gdmmVideo.getInitPraiseNum());
                //从redis中获取短视频点击量 ======注意：这里 gdmmVideo.getViewed() 其实是InitView，这样返回到前台InitView 是null。
                gdmmVideoResDTO.setViewed(CommonUtil.redisCommonFind(RedisKey.VIDEO_CLICKNUM + "_" + id).intValue() + gdmmVideo.getInitView());
                gdmmVideoResDTO.setCommentNum(CommonUtil.redisCommonFind(Constants.SHORT_VIDEO_STR + "_" + Constants.COMMENTNUM + "_" + id).intValue());
                //设置收藏信息
//                collectInfo(sessionUid, gdmmVideo);
                //20241028来源为空且第三方来源不为空，则使用第三方来源
                if (StrUtil.isEmpty(gdmmVideoResDTO.getSource()) && StrUtil.isNotEmpty(gdmmVideo.getSourceNameForThird())) {
                    gdmmVideoResDTO.setSource(gdmmVideo.getSourceNameForThird());
                }
                gdmmVideoResDTOS.add(gdmmVideoResDTO);
            }
        }
        return gdmmVideoResDTOS;
    }

    /**
     * 获取全局配置信息
     */
    private Map<String, Integer> getSwitchInfo() {
        Map<String, Integer> switchInfoMap = new HashMap<String, Integer>();
        //新闻是否开启评论(先根据总设置，然后再根据单条新闻设置)
        Integer isComment = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_COMMENT.getKey());
        switchInfoMap.put("isComment", isComment);
        //新闻是否开启评论审核
        Integer isExamine = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_EXAMINE.getKey());
        switchInfoMap.put("isExamine", isExamine);
        //新闻是否显示阅读量
        Integer isClickNum = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_CLICKNUM.getKey());
        switchInfoMap.put("isClickNum", isClickNum);
        //新闻是否显示来源
        Integer isSource = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_SOURCE.getKey());
        switchInfoMap.put("isSource", isSource);
        //新闻是否显示标签
        Integer isLabel = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_LABEL.getKey());
        switchInfoMap.put("isLabel", isLabel);
        //新闻是否显示相关新闻
        Integer isShowNews = getSettingValueByRedisKey(RedisKey.ARTICLE_IS_SHOWNEWS.getKey());
        switchInfoMap.put("isShowNews", isShowNews);
        return switchInfoMap;
    }

    private void assembleData(GdmmArticleResDTO gdmmArticle, Map<String, Integer> switchInfoMap) {
        /**
         * 添加全局配置信息
         */
        if (null != switchInfoMap && switchInfoMap.size() > 0) {
            //新闻是否开启评论(先根据总设置，然后再根据单条新闻设置)
            gdmmArticle.setIsComment(switchInfoMap.get("isComment") == 1 ? gdmmArticle.getIsComment() : 0);
            //新闻是否开启评论审核
            gdmmArticle.setIsExamine(switchInfoMap.get("isExamine"));
            //新闻是否显示阅读量
            gdmmArticle.setIsClickNum(switchInfoMap.get("isClickNum"));
            //新闻是否显示来源
            gdmmArticle.setIsSource(switchInfoMap.get("isSource"));
            //新闻是否显示标签
            gdmmArticle.setIsLabel(switchInfoMap.get("isLabel"));
            //新闻是否显示相关新闻
            gdmmArticle.setIsShowNews(switchInfoMap.get("isShowNews"));
        }
        //视频图片路径
        gdmmArticle.setVideoImgUrl(OSSUtil.getImageURL(gdmmArticle.getVideoImgUrl()));
        //新闻列表图片
        if (StrUtil.isNotBlank(gdmmArticle.getImgUrl())) {
            if (Constants.ARTICLE_TYPE_ORDINARY_PICTURE.equals(gdmmArticle.getDisplayMode()) || Constants.ARTICLE_TYPE_SINGLE_PICTURE.equals(gdmmArticle.getDisplayMode())) {
                gdmmArticle.setImgUrl(OSSUtil.getImageURL(gdmmArticle.getImgUrl()));
            } else if (Constants.ARTICLE_TYPE_MANY_PICTURE.equals(gdmmArticle.getDisplayMode())) {
                String temp = "";
                String[] articleImgArray = gdmmArticle.getImgUrl().split(";");
                for (String imgUrl : articleImgArray) {
                    temp += OSSUtil.getImageURL(imgUrl) + ";";
                }
                gdmmArticle.setImgUrl(temp.length() > 1 ? temp.substring(0, temp.length() - 1) : temp);
            }
        }

        //兰州使用指定图片
        if (StrUtil.isNotBlank(gdmmArticle.getImgUrl())) {
            gdmmArticle.setShareImgUrl(gdmmArticle.getImgUrl().split(";")[0]);
        }

        //新闻来源开启，展示来源完整图片路径
        if (null != gdmmArticle.getIsSource() && gdmmArticle.getIsSource() == 1) {
            gdmmArticle.setSourceImgUrl(gdmmArticle.getSourceImgUrl() == null ? "" : OSSUtil.getImageURL(gdmmArticle.getSourceImgUrl()));
        }
        //新闻标签开启，展示标签完整图片路径
        if (null != gdmmArticle.getIsLabel() && gdmmArticle.getIsLabel() == 1) {
            gdmmArticle.setLabelImgUrl(gdmmArticle.getLabelImgUrl() == null ? "" : OSSUtil.getImageURL(gdmmArticle.getLabelImgUrl()));
        }

        //20241028来源为空且第三方来源不为空，则使用第三方来源
        if (StrUtil.isEmpty(gdmmArticle.getSource()) && StrUtil.isNotEmpty(gdmmArticle.getSourceNameForThird())) {
            gdmmArticle.setSource(gdmmArticle.getSourceNameForThird());
        }
    }
}
