package com.zainanjing.ccms.controller;


import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.StrUtil;
import com.ruoyi.common.utils.id.IdUtil;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.common.utils.sign.SignUtil;
import com.zainanjing.ccms.track.domain.CsAppTrack;
import com.zainanjing.ccms.track.domain.CsAppTrackH5;
import com.zainanjing.ccms.track.service.ICsAppTrackH5Service;
import com.zainanjing.ccms.track.service.ICsAppTrackService;
import com.zainanjing.convenience.support.web.ResResult;
import jakarta.annotation.Resource;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/api-mix")
public class TrackController {

    private final String H5_TRACK_COOKIE_NAME = "ccms_cc_track_h5";

    private final String H5_TRACK_COOKIE_CODE = "ccms_changchun_h5_track"; //加密密码

    @Resource
    private ICsAppTrackH5Service csAppTrackH5Service;

    @Resource
    private ICsAppTrackService csAppTrackService;

    @Resource
    private RedisCache redisCache;

    @PostMapping("/track/h5")
    public void trackH5(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        String userId = null;
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (H5_TRACK_COOKIE_NAME.equals(cookie.getName())) {
                    userId = cookie.getValue();
                    break;
                }
            }
        }
        if (userId == null) {
            String uuid = IdUtil.fastSimpleUUID();
            userId = uuid + SignUtil.md5(H5_TRACK_COOKIE_CODE + "-" + uuid); // 生成新的用户ID
            Cookie trackCookie = new Cookie(H5_TRACK_COOKIE_NAME, userId);
            trackCookie.setPath("/changchun");// 设置路径为 /changchun
            trackCookie.setMaxAge(365 * 24 * 60 * 60);  // 有效期1年
            trackCookie.setSecure(true);  // 仅在HTTPS下传输（生产环境推荐
            trackCookie.setHttpOnly(true);  //防止JS读取，增强安全性
            trackCookie.setAttribute("SameSite", "None");  // 跨域场景必须设置
            response.addCookie(trackCookie);
            redisCache.set("changchun:ccms:track:h5:" + uuid, 1, 10, TimeUnit.SECONDS); //防止不停刷新
        } else {
            // 如果用户ID已存在，验证签名是否正确
            String uuid = userId.substring(0, 32); // 提取UUID部分
            String signature = userId.substring(32); // 提取签名部分
            if (!SignUtil.md5(H5_TRACK_COOKIE_CODE + "-" + uuid).equals(signature)) {
                log.error("无效的用户ID: {}", userId);
                return;
            }
            if (redisCache.hasKey("changchun:ccms:track:h5:" + uuid)) {
                log.debug("重复的H5追踪请求: {}", userId);
                return; // 防止重复追踪
            } else {
                redisCache.set("changchun:ccms:track:h5:" + uuid, 1, 10, TimeUnit.SECONDS); //防止不停刷新
            }
        }
        CsAppTrackH5 csAppTrackH5 = new CsAppTrackH5();
        csAppTrackH5.setCookieId(userId);
        csAppTrackH5.setUa(request.getHeader("User-Agent"));
        csAppTrackH5.setIp(IpUtils.getIpAddr(request));
        csAppTrackH5.setRecordTs(System.currentTimeMillis());
        csAppTrackH5Service.save(csAppTrackH5);
    }

    @PostMapping("/track/app")
    public ResResult trackApp(@RequestBody CsAppTrack csAppTrack, HttpServletRequest request) {
        if (StrUtil.isBlank(csAppTrack.getDeviceId())
                || Objects.isNull(csAppTrack.getCallType())
                || StrUtil.isBlank(csAppTrack.getTrackNo())
                || StrUtil.isBlank(csAppTrack.getSign())
                || Objects.isNull(csAppTrack.getRecordTs())) {
            log.error("客户端日志追踪参数不完整: {}", csAppTrack);
            return ResResult.error("参数不完整");
        }

        String sign = SignUtil.md5(StrUtil.format("{}{}{}", "b4bdf8748c035bd88fd75e409dfd82c0", csAppTrack.getDeviceId(), csAppTrack.getRecordTs()));
        if (!StrUtil.equals(sign, csAppTrack.getSign())) {
            return ResResult.success();
        }
        csAppTrack.setUa(request.getHeader("User-Agent"));
        csAppTrack.setIp(IpUtils.getIpAddr(request));
        csAppTrackService.save(csAppTrack);
        return ResResult.success();
    }

}
