#开发环境配置
spring:
  main:
    lazy-initialization: true
  #data source config
  datasource:
    druid:
      master:
        url: **********************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
      gdmm:
        enabled: true
        url: ************************************_${system.city}?useUnicode=true&characterEncoding=utf-8&useSSL=false
        username: bmuser
        password: bmuserA789F
  #redis
  data:
    redis:
      database: 1
      host: *************
      port: 6379
      password: K4UMDap0
      timeout: 30000
      pool:
        maxActive: 24
        maxWait: -1
        maxIdle: 10
        minIdle: 0
  jms:
    pub-sub-domain: false
  elasticsearch:
    rest:
      uris: http://*************:9200
      password: 6jDhjr^GQGqv
      username: elastic


server:
  servlet:
    context-path: /${system.city}/api
  port: 9285

# 推送配置
push:
  production: false

system:
  tenants:
    flag: false
  api-url: https://ccms.gdmmdev.com/${system.city}/api
  admin-url: https://ccms.gdmmdev.com/${system.city}
  sms-flag: true
  user-center-url: https://ucenter${system.city}t.zainanjing365.com/usercenter/
  site-app-url: https://oriental${system.city}t.zainanjing365.com/siteapp/
  site-app-token: 0Q8rmH7nMKYOHXIU1
  site-app-uid: 2825392
  wap-inter:
    app-id: 110
    app-key: 1692866d6363c0bd792dc7e7346d81aa
sms:
  binli:
    account: "010005"
    password: 8V2JNVdR
    url: http://*************:7891/api/v1/send

wangyi:
  check-url: https://imagecensoringt.zainanjing365.com
  check-client: client1
  check-key: dcc7c193b6ef484ab47338b08d6e1e6d

ruoyi:
  allowedOrigins:
    - https://*.zainanjing365.com
    - https://*.njgdmm.com
    - https://*.gdmmdev.com

ali-oss:
  endpoint: https://oss-cn-hangzhou.aliyuncs.com
  url: https://img.shoptest.zainanjing365.com/