#!/bin/bash

# 定义变量
BUILD_ENV="prod"
LOCAL_BUILD_DIR="site-api/target"
REMOTE_USER="root"
REMOTE_HOST="*************"
REMOTE_DIR="/data/deploy"
JAR_FILE="changchunbm-api.jar"  # 根据实际jar包名称调整

# 检查jar文件是否存在且在一分钟内创建
JAR_PATH="$LOCAL_BUILD_DIR/$JAR_FILE"
SKIP_BUILD=false

if [ -f "$JAR_PATH" ]; then
    # 获取文件的创建时间（秒）
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        FILE_TIME=$(stat -f %B "$JAR_PATH")
    else
        # Linux
        FILE_TIME=$(stat -c %Y "$JAR_PATH")
    fi

    # 获取当前时间（秒）
    CURRENT_TIME=$(date +%s)

    # 计算时间差（秒）
    TIME_DIFF=$((CURRENT_TIME - FILE_TIME))

    # 如果文件在60秒（1分钟）内创建，则跳过构建
    if [ $TIME_DIFF -lt 60 ]; then
        echo "JAR file $JAR_FILE was created within the last minute (${TIME_DIFF}s ago). Skipping build..."
        SKIP_BUILD=true
    else
        echo "JAR file $JAR_FILE is older than 1 minute (${TIME_DIFF}s ago). Rebuilding..."
    fi
else
    echo "JAR file $JAR_FILE not found. Building..."
fi

# Maven 重新打包（如果需要）
if [ "$SKIP_BUILD" = false ]; then
    echo "Building the project with Maven for $BUILD_ENV environment..."
    mvn clean package -Dmaven.test.skip=true

    # 检查编译是否成功
    if [ $? -ne 0 ]; then
        echo "Maven build failed. Exiting..."
        exit 1
    fi
else
    echo "Skipping Maven build as JAR file is recent."
fi

# 传输文件到服务器指定文件夹
echo "Transferring files to the server..."
ssh $REMOTE_USER@$REMOTE_HOST "mkdir -p $REMOTE_DIR"
scp $LOCAL_BUILD_DIR/$JAR_FILE $REMOTE_USER@$REMOTE_HOST:$REMOTE_DIR

# 检查传输是否成功
if [ $? -ne 0 ]; then
  echo "File transfer failed. Exiting..."
  exit 1
fi
