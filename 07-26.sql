create table lc_category
(
    id          bigint auto_increment comment '编号'
        primary key,
    parent_id   bigint                               null comment '父编号',
    name        varchar(50)                          not null comment '名称',
    pics        varchar(200)                         null comment '图片',
    sort_num    bigint     default 0                 null comment '排序',
    is_hot      tinyint(1) default 0                 not null comment '热门分类',
    enabled     tinyint(1) default 1                 not null comment '是否显示:
0 : 隐藏
1: 显示',
    del_flag    tinyint(1) default 0                 null comment '是否删除，1:删除',
    create_by   varchar(64)                          not null comment '创建者',
    create_time datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_by   varchar(64)                          null comment '更新者',
    update_time datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    tenant_id   varchar(10)                          null comment '租户code'
)
    comment '分类';

# 计算客户端日活数数据字段
# 字段名 字段含义 取值说明 开发完成
# 时限
# ua user-agent 8 月底
# device_id
# 移动客户端设备唯一标
# 识
# 核心字段
# 7 月底
# uid 用户注册 ID 8 月底
# os 客户端操作系统
# 枚举型，Android: Android 系
# 统，iOS：iOS 系统，
# OpenHarmony: 鸿蒙系统
# 8 月底
# os_version 客户端操作系统版本 如 1.0.0 8 月底
# brand
# 客户端所用终端品牌信
# 息
# iPhone、HUAWEI、VIVO、
# OPPO、XIAOMI 等。 8 月底
# device_model
# 客户端所用终端设备型
# 号信息 如 ELS-AN10 等 8 月底
# carrier
# 客户端网络所属运营商
# 信息
# 中国移动、中国联通、中国电
# 信等 8 月底
# access 客户端网络接入类型 无网、2G、3G、4G、Wifi
# 等。枚举型，-1: 其他未定 8 月底
# 2
# 义,0: 无网,1: Wi-Fi,2: 2G,3:
# 3G,4: 4G,5:其他类型网络。
# record_ts
# 日志产生时的时间戳，
# 毫秒 1709789494393， 8 月底
# ip 客户端用户 ip 地址 ********* 8 月底
# 2 计算端外 H5 日活数数据字段
# 字段名 字段含义 取值说明 开发完成
# 时限
# ua user-agent 浏览器 ua 8 月底
# cookie_id
# 用户浏览器 cookie 唯
# 一标识
# 在浏览器中的 cookie 植入用户标
# 识，有效期为一年。
# 核心字段
# 7 月底
# ip 端外 ip 地址 8 月底
# 3 计算客户端日人均使用时长数据字段
# 说明：该数据包括用户启动客户端的日志和退出客户端的日
# 志，核心字段除包括 1 中的字段外，在日志中用以下字段标识客
# 户端启动和退出。
# 字段名 字段含义 取值说明 开发完成
# 时限
# call_type 客户端启动或者退出 枚举：0 代表启动，1 代表退出 8 月底
# record_ts 日志产生时的时间
# 戳，毫秒
# 1709789494393 核心字段
# 7 月底

drop table if exists cs_app_track;
create table cs_app_track(
    id bigint auto_increment comment '编号'
        primary key,
    ua varchar(255) null comment 'ua',
    uid varchar(50) null comment '用户注册 ID',
    device_id varchar(50) null comment '设备id',
    os varchar(20) null comment '操作系统',
    os_version varchar(20) null comment '操作系统版本',
    brand varchar(20) null comment '品牌',
    device_model varchar(50) null comment '设备型号',
    carrier varchar(20) null comment '运营商',
    access varchar(20) null comment '网络接入类型',
    record_ts bigint null comment '日志产生时的时间戳，毫秒',
    duration bigint null comment '使用时长，毫秒',
    ip varchar(50) null comment '客户端用户 ip 地址',
    call_type tinyint null comment '客户端启动或者退出:0 代表启动，1 代表退出',
    track_no varchar(36) null comment '日志追踪号',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
) comment '客户端日志追踪表';
# 添加字段duration_type
alter table cs_app_track add column duration_type tinyint default 0 comment '使用时长类型:1 前台使用; 2 后台使用';


drop table if exists cs_app_track_h5;
create table cs_app_track_h5(
    id bigint auto_increment comment '编号'
        primary key,
    ua varchar(255) null comment 'ua',
    cookie_id varchar(80) null comment '用户浏览器 cookie 唯一标识',
    ip varchar(50) null comment '客户端用户 ip 地址',
    record_ts bigint not null comment '日志产生时的时间戳，毫秒',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间'
) comment '客户端日志追踪表 H5';


# 每日统计数据表
drop table if exists cs_daily_stats;
create table cs_daily_stats(
    id bigint auto_increment comment '编号'
        primary key,
    stat_date date not null comment '统计日期',
    total_dau int default 0 comment '总日活数（APP+H5去重）',
    h5_dau int default 0 comment 'H5日活数',
    app_dau int default 0 comment 'APP日活数',
    app_avg_duration bigint default 0 comment 'APP日人均使用时长（毫秒）',
    app_total_duration bigint default 0 comment 'APP总使用时长（毫秒）',
    create_time datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    unique key uk_stat_date (stat_date) comment '统计日期唯一索引'
) comment '每日统计数据表';


select * from cs_daily_stats;

select * from cs_app_track_h5;