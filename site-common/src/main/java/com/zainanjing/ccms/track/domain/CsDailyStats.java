package com.zainanjing.ccms.track.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDate;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 每日统计数据对象 cs_daily_stats
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@TableName(value = "cs_daily_stats")
public class CsDailyStats implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * 统计日期
     */
    private LocalDate statDate;

    /**
     * 总日活数（APP+H5去重）
     */
    private Long totalDau;

    /**
     * H5日活数
     */
    private Long h5Dau;

    /**
     * APP日活数
     */
    private Long appDau;

    /**
     * APP日人均使用时长（毫秒）
     */
    private Long appAvgDuration;

    /**
     * APP总使用时长（毫秒）
     */
    private Long appTotalDuration;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}
