package com.zainanjing.ccms.track.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户端日志追踪 H5对象 cs_app_track_h5
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@TableName(value = "cs_app_track_h5")
public class CsAppTrackH5 implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * ua
     */
    private String ua;

    /**
     * 用户浏览器 cookie 唯一标识
     */
    private String cookieId;

    /**
     * 客户端用户 ip 地址
     */
    private String ip;

    /**
     * 日志产生时的时间戳，毫秒
     */
    private Long recordTs;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

}
