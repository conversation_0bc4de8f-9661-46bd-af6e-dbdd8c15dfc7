package com.zainanjing.ccms.track.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 客户端日志追踪对象 cs_app_track
 *
 * <AUTHOR>
 * @date 2025-07-26
 */
@Data
@TableName(value = "cs_app_track")
public class CsAppTrack implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId
    private Long id;

    /**
     * ua
     */
    private String ua;

    /**
     * 用户注册 ID
     */
    private String uid;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 运营商
     */
    private String carrier;

    /**
     * 网络接入类型
     */
    private String access;

    /**
     * 日志产生时的时间戳，毫秒
     */
    private Long recordTs;

    /**
     * 客户端用户 ip 地址
     */
    private String ip;

    /**
     * 客户端启动或者退出:0 代表启动，1 代表退出
     */
    private Integer callType;

    /**
     *使用时长
     */
    private Long duration;

    /**
     * 时长类型 1 前台使用 2 后台使用
     */
    private Integer durationType;

    /**
     * 日志追踪号
     */
    private String trackNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private String sign; // 签名字段，用于验证数据完整性

}
