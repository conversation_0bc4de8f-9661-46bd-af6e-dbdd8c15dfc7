package com.ruoyi.common.utils.cookie;

import com.ruoyi.common.utils.StrUtil;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Pattern;

/**
 * Cookie工具类
 * 提供跨浏览器兼容的Cookie设置功能
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Slf4j
public class CookieUtil {

    // Safari浏览器兼容性检测的正则表达式
    private static final Pattern CHROME_PATTERN = Pattern.compile("Chrome/([0-9]+)");
    private static final Pattern SAFARI_PATTERN = Pattern.compile("Version/([0-9]+).*Safari");
    private static final Pattern IOS_PATTERN = Pattern.compile("OS ([0-9]+)_([0-9]+)");
    private static final Pattern MACOS_PATTERN = Pattern.compile("OS X ([0-9]+)_([0-9]+)");
    private static final Pattern UC_BROWSER_PATTERN = Pattern.compile("UCBrowser/([0-9]+)\\.([0-9]+)");
    private static final Pattern CHROMIUM_PATTERN = Pattern.compile("Chromium/([0-9]+)");

    /**
     * 检测浏览器是否支持SameSite=None属性
     * 某些版本的Safari和Chrome不支持SameSite=None，会将其错误解析为SameSite=Strict
     * 
     * @param userAgent 浏览器User-Agent字符串
     * @return true表示支持SameSite=None，false表示不支持
     */
    public static boolean shouldUseSameSiteNone(String userAgent) {
        if (StrUtil.isBlank(userAgent)) {
            return false;
        }

        // 检测不兼容的Chrome版本 (51-66)
        java.util.regex.Matcher chromeMatcher = CHROME_PATTERN.matcher(userAgent);
        if (chromeMatcher.find()) {
            int chromeVersion = Integer.parseInt(chromeMatcher.group(1));
            if (chromeVersion >= 51 && chromeVersion <= 66) {
                log.debug("检测到不兼容的Chrome版本: {}", chromeVersion);
                return false;
            }
        }

        // 检测不兼容的Chromium版本
        java.util.regex.Matcher chromiumMatcher = CHROMIUM_PATTERN.matcher(userAgent);
        if (chromiumMatcher.find()) {
            int chromiumVersion = Integer.parseInt(chromiumMatcher.group(1));
            if (chromiumVersion >= 51 && chromiumVersion <= 66) {
                log.debug("检测到不兼容的Chromium版本: {}", chromiumVersion);
                return false;
            }
        }

        // 检测iOS Safari (iOS 12及以下不支持)
        if (userAgent.contains("iPhone") || userAgent.contains("iPad")) {
            java.util.regex.Matcher iosMatcher = IOS_PATTERN.matcher(userAgent);
            if (iosMatcher.find()) {
                int majorVersion = Integer.parseInt(iosMatcher.group(1));
                if (majorVersion <= 12) {
                    log.debug("检测到不兼容的iOS版本: {}", majorVersion);
                    return false;
                }
            }
        }

        // 检测macOS Safari (macOS 10.14及以下不支持)
        if (userAgent.contains("Safari") && userAgent.contains("Macintosh")) {
            java.util.regex.Matcher macosMatcher = MACOS_PATTERN.matcher(userAgent);
            if (macosMatcher.find()) {
                int majorVersion = Integer.parseInt(macosMatcher.group(1));
                int minorVersion = Integer.parseInt(macosMatcher.group(2));
                if (majorVersion < 10 || (majorVersion == 10 && minorVersion <= 14)) {
                    log.debug("检测到不兼容的macOS版本: {}.{}", majorVersion, minorVersion);
                    return false;
                }
            }
        }

        // 检测UC浏览器 (某些版本不支持)
        java.util.regex.Matcher ucMatcher = UC_BROWSER_PATTERN.matcher(userAgent);
        if (ucMatcher.find()) {
            int majorVersion = Integer.parseInt(ucMatcher.group(1));
            int minorVersion = Integer.parseInt(ucMatcher.group(2));
            if (majorVersion < 12 || (majorVersion == 12 && minorVersion < 13)) {
                log.debug("检测到不兼容的UC浏览器版本: {}.{}", majorVersion, minorVersion);
                return false;
            }
        }

        return true;
    }

    /**
     * 设置跨域兼容的Cookie
     * 根据浏览器兼容性自动决定是否设置SameSite=None属性
     * 
     * @param cookie Cookie对象
     * @param request HTTP请求对象，用于获取User-Agent
     */
    public static void setSameSiteNoneIfSupported(Cookie cookie, HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (shouldUseSameSiteNone(userAgent)) {
            cookie.setAttribute("SameSite", "None");
            log.debug("浏览器支持SameSite=None，已设置该属性");
        } else {
            log.debug("浏览器不支持SameSite=None，跳过设置该属性");
            // 对于不支持的浏览器，不设置SameSite属性，让浏览器使用默认行为
        }
    }

    /**
     * 创建跨域兼容的Cookie
     * 
     * @param name Cookie名称
     * @param value Cookie值
     * @param path Cookie路径
     * @param maxAge Cookie有效期（秒）
     * @param secure 是否仅在HTTPS下传输
     * @param httpOnly 是否防止JS读取
     * @param request HTTP请求对象，用于获取User-Agent
     * @return 配置好的Cookie对象
     */
    public static Cookie createCrossDomainCookie(String name, String value, String path, 
                                                int maxAge, boolean secure, boolean httpOnly, 
                                                HttpServletRequest request) {
        Cookie cookie = new Cookie(name, value);
        cookie.setPath(path);
        cookie.setMaxAge(maxAge);
        cookie.setSecure(secure);
        cookie.setHttpOnly(httpOnly);
        
        // 设置跨域兼容的SameSite属性
        setSameSiteNoneIfSupported(cookie, request);
        
        return cookie;
    }
}
